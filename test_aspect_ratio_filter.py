#!/usr/bin/env python
"""
测试 aspect_ratio 过滤功能的脚本
"""

import os
import sys
import django

# 设置 Django 环境
sys.path.insert(0, '/Users/<USER>/workspace/wallnest')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.db.models import Q
from wallpapers.utils import numfilter
from wallpapers import models

def test_aspect_ratio_filter():
    """测试 aspect_ratio 过滤逻辑"""
    
    # 模拟解析后的约束组
    # 例如：aspect_ratio=[">=1.5,<2.0", ">=0.5,<1.0"]
    aspect_ratio = [
        [
            numfilter.Constraint(operator=numfilter.Operator.GTE, value=1.5),
            numfilter.Constraint(operator=numfilter.Operator.LT, value=2.0),
        ],
        [
            numfilter.Constraint(operator=numfilter.Operator.GTE, value=0.5),
            numfilter.Constraint(operator=numfilter.Operator.LT, value=1.0),
        ]
    ]
    
    # 构建查询逻辑（复制自实现的代码）
    q_objects = []
    
    for constraint_group in aspect_ratio:
        # 每个组内的约束是 AND 关系
        group_q = Q()
        
        for constraint in constraint_group:
            if constraint.operator == numfilter.Operator.EQ:
                group_q &= Q(aspect_ratio=constraint.value)
            elif constraint.operator == numfilter.Operator.GTE:
                group_q &= Q(aspect_ratio__gte=constraint.value)
            elif constraint.operator == numfilter.Operator.LTE:
                group_q &= Q(aspect_ratio__lte=constraint.value)
            elif constraint.operator == numfilter.Operator.GT:
                group_q &= Q(aspect_ratio__gt=constraint.value)
            elif constraint.operator == numfilter.Operator.LT:
                group_q &= Q(aspect_ratio__lt=constraint.value)
        
        if group_q.children:  # 只有当组内有约束时才添加
            q_objects.append(group_q)
    
    # 将所有组用 OR 连接
    if q_objects:
        final_q = q_objects[0]
        for q in q_objects[1:]:
            final_q |= q
        
        print("生成的查询条件:")
        print(final_q)
        
        # 测试查询
        queryset = models.WallpaperImage.objects.filter(final_q)
        print(f"查询结果数量: {queryset.count()}")
        
        # 显示一些示例结果
        for wallpaper in queryset[:5]:
            print(f"ID: {wallpaper.id}, aspect_ratio: {wallpaper.aspect_ratio}")

def test_single_constraint():
    """测试单个约束"""
    aspect_ratio = [
        [numfilter.Constraint(operator=numfilter.Operator.EQ, value=1.78)]
    ]
    
    q_objects = []
    for constraint_group in aspect_ratio:
        group_q = Q()
        for constraint in constraint_group:
            if constraint.operator == numfilter.Operator.EQ:
                group_q &= Q(aspect_ratio=constraint.value)
            elif constraint.operator == numfilter.Operator.GTE:
                group_q &= Q(aspect_ratio__gte=constraint.value)
            elif constraint.operator == numfilter.Operator.LTE:
                group_q &= Q(aspect_ratio__lte=constraint.value)
            elif constraint.operator == numfilter.Operator.GT:
                group_q &= Q(aspect_ratio__gt=constraint.value)
            elif constraint.operator == numfilter.Operator.LT:
                group_q &= Q(aspect_ratio__lt=constraint.value)
        
        if group_q.children:
            q_objects.append(group_q)
    
    if q_objects:
        final_q = q_objects[0]
        for q in q_objects[1:]:
            final_q |= q
        
        print("\n单个约束测试:")
        print(final_q)
        
        queryset = models.WallpaperImage.objects.filter(final_q)
        print(f"查询结果数量: {queryset.count()}")

if __name__ == "__main__":
    print("测试 aspect_ratio 过滤功能...")
    test_aspect_ratio_filter()
    test_single_constraint()
