from dataclasses import dataclass
import enum


class Operator(enum.StrEnum):
    EQ = "="
    NE = "!="
    GT = ">"
    LT = "<"
    GTE = ">="
    LTE = "<="


@dataclass(kw_only=True)
class Constraint:
    operator: Operator
    value: int | float


def parse_constraints(expr: str) -> list[Constraint]:
    """
    Parse constraint expressions into a list of Constraint objects.

    Args:
        expr: Expression string like "1.1", ">=1.23", ">=1,<2.3", "<1.2"

    Returns:
        List of Constraint objects

    Raises:
        ValueError: If the expression is invalid
    """
    if not expr or not expr.strip():
        raise ValueError("Empty expression")

    # Split by comma and filter out empty parts
    parts = [part.strip() for part in expr.split(",")]

    # Check for empty parts (leading/trailing commas or consecutive commas)
    if any(not part for part in parts):
        raise ValueError("Invalid expression: empty constraint part")

    constraints = []

    for part in parts:
        constraint = _parse_single_constraint(part)
        constraints.append(constraint)

    return constraints


def _parse_single_constraint(part: str) -> Constraint:
    """Parse a single constraint part like '>=1.23' or '1.1'"""
    part = part.strip()

    if not part:
        raise ValueError("Empty constraint part")

    # Try to match operators in order of length (longest first to avoid conflicts)
    operators_by_length = sorted(
        [(op.value, op) for op in Operator], key=lambda x: len(x[0]), reverse=True
    )

    for op_str, operator in operators_by_length:
        if part.startswith(op_str):
            value_str = part[len(op_str) :].strip()
            if not value_str:
                raise ValueError(f"Missing value after operator '{op_str}'")

            try:
                # Try to parse as int first, then float
                if "." in value_str:
                    value = float(value_str)
                else:
                    value = int(value_str)
            except ValueError:
                raise ValueError(f"Invalid number: '{value_str}'")

            return Constraint(operator=operator, value=value)

    # No operator found, assume equality
    try:
        if "." in part:
            value = float(part)
        else:
            value = int(part)
    except ValueError:
        raise ValueError(f"Invalid number: '{part}'")

    return Constraint(operator=Operator.EQ, value=value)
