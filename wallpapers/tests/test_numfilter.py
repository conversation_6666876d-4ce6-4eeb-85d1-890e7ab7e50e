import pytest

from wallpapers.utils import numfilter


@pytest.mark.parametrize(
    "expr,expected",
    [
        ("1.1", [numfilter.Constraint(operator=numfilter.Operator.EQ, value=1.1)]),
        (">=1.23", [numfilter.Constraint(operator=numfilter.Operator.GTE, value=1.23)]),
        (
            ">=1,<2.3",
            [
                numfilter.Constraint(operator=numfilter.Operator.GTE, value=1),
                numfilter.Constraint(operator=numfilter.Operator.LT, value=2.3),
            ],
        ),
        (
            "<1.2",
            [numfilter.Constraint(operator=numfilter.Operator.LT, value=1.2)],
        ),
    ],
)
def test_parse_constraints_on_success(expr, expected):
    assert numfilter.parse_constraints(expr) == expected


@pytest.mark.parametrize(
    "expr",
    [
        ">=1.,1.3",
        ">=1.2,",
        ",<1.2",
        ">=1.2,<1.3,",
        ",>=1.2,<1.3",
        "a",
        ">=a",
    ],
)
def test_parse_constraints_on_failure(expr):
    with pytest.raises(ValueError):
        numfilter.parse_constraints(expr)
