import datetime
import functools
import typing
from contextvars import ContextVar
from inspect import iscoroutinefunction

import jwt
from django.conf import settings
from django.contrib.auth.models import User
from django.core.cache import cache
from django.http import HttpRequest, HttpResponse

import openapi

OAS_SECURITY_KEY = "access_token"  # 用于标记 OpenAPI securiry
_ALGORITHM = "HS256"
_SECRET_KEY = settings.SECRET_KEY


class _GetUserPermissions:
    __cache_key_template = "user_permissions::%s"

    def __call__(self, user: User) -> set[str]:
        from users.models import Role

        cache_key = self.__cache_key_template % user.pk
        rv = cache.get(cache_key)
        if rv is None:
            roles = (
                Role.objects.filter(users=user)
                .prefetch_related("roleapipermission_set")
                .all()
            )
            rv = set(
                permission.operation_id
                for role in roles
                for permission in role.roleapipermission_set.all()  # type: ignore
            )
            cache.set(cache_key, rv, 60 * 60)
        return rv

    def clear_cache(self, user: User):
        cache.delete(self.__cache_key_template % user.pk)


user_permissions = _GetUserPermissions()


def _header_authorization(request: HttpRequest):
    return request.headers.get("Authorization")


def _authenticate_user(authorization: str):
    scheme, _, token = authorization.partition(" ")
    if scheme != "Bearer" or not token:
        return None

    try:
        payload = jwt.decode(token, _SECRET_KEY, algorithms=[_ALGORITHM])
    except jwt.InvalidTokenError:
        return None

    try:
        user = User.objects.get(pk=payload["user_id"])
    except User.DoesNotExist:
        return None
    return user


user_ctx = ContextVar("user")


@typing.overload
def s_authenticate() -> User: ...


@typing.overload
def s_authenticate(*, required=False) -> User | None: ...


def s_authenticate(*, required=True):
    def fn(name: str):
        def decorator(func):
            security = [{OAS_SECURITY_KEY: []}]
            if not required:
                security.append({})

            @openapi.declare(security=security)
            @openapi.response(401)
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                request = args[0] if isinstance(args[0], HttpRequest) else args[1]
                authorization = _header_authorization(request)
                if authorization is None:
                    user = None
                else:
                    user = _authenticate_user(authorization)

                if required and user is None:
                    return HttpResponse(status=401)

                kwargs[name] = user

                token = user_ctx.set(user)
                try:
                    return func(*args, **kwargs)
                finally:
                    user_ctx.reset(token)

            return wrapper

        return decorator

    return typing.cast(User | None, openapi.S(fn))


def required_jwt(
    _func=None, /, userkey: str | None = None, permission: str | None = None
):
    def decorator(func):
        if iscoroutinefunction(func):
            raise NotImplementedError
        else:

            @openapi.declare(security=[{OAS_SECURITY_KEY: []}])
            @openapi.response(401)
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                request = args[0] if isinstance(args[0], HttpRequest) else args[1]

                authorization = _header_authorization(request)
                if authorization is None:
                    return HttpResponse(status=401)

                user = _authenticate_user(authorization)
                if user is None:
                    return HttpResponse(status=401)

                if permission is not None and not user.is_superuser:
                    permissions = user_permissions(user)
                    if permission not in permissions:
                        return HttpResponse(status=403)

                if userkey is not None:
                    kwargs[userkey] = user

                return func(*args, **kwargs)

        if permission is not None:
            wrapper = openapi.response(403)(wrapper)
        return wrapper

    if _func is not None:
        return decorator(_func)

    return decorator


def generate_access_token(user_id):
    now = datetime.datetime.now(datetime.timezone.utc)
    payload = {
        "user_id": user_id,
        "exp": now + datetime.timedelta(minutes=5),
        "iat": now,
    }
    return jwt.encode(payload, _SECRET_KEY, algorithm=_ALGORITHM)
