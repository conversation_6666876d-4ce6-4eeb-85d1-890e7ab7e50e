import typing
import uuid

import zangar as z
from django.db.models import Model
from django.db.models.query import QuerySet
from django.http import Http404, JsonResponse

import openapi
from wallpapers import models
from wallpapers.auth import s_authenticate
from wallpapers.utils import numfilter

from .base import APIView
from .common import s_query_image_sizes
from .schemas import (
    clienttopic_struct,
    sql_integer_schema,
    uploader_struct,
    wallpaper_struct,
)


def _paging_response_schema(item_schema: z.Schema, /):
    return z.struct(
        {
            "results": z.to.list(item_schema),
            "page": z.int().gte(1),
            "page_size": z.int().gte(0),
            "total": z.int().gte(0),
        }
    )


M = typing.TypeVar("M", bound=Model)


def _paginate(queryset: QuerySet[M], page: int, page_size: int) -> QuerySet[M]:
    return queryset[(page - 1) * page_size : page * page_size]


def _paging_response_format(results, page: int, page_size: int, total: int):
    return {
        "results": results,
        "page": page,
        "page_size": page_size,
        "total": total,
    }


def _paging_response(queryset: QuerySet[M], page: int, page_size: int):
    return _paging_response_format(
        _paginate(queryset, page, page_size),
        page,
        page_size,
        queryset.count(),
    )


s_query_page = openapi.s_query(
    name="page", schema=z.to.int().gte(1).lte(1000), py_default=1
)
s_query_page_size = openapi.s_query(
    name="page_size", schema=z.to.int().gte(0).lte(1000), py_default=10
)
s_path_client_id = openapi.s_path(
    name="client_id", schema=z.str().transform(lambda x: uuid.UUID(x, version=4))
)
s_path_wallpaper_id = openapi.s_path(
    name="wallpaper_id", schema=z.to.int().relay(sql_integer_schema)
)
s_path_topic_id = openapi.s_path(
    name="topic_id", schema=z.to.int().relay(sql_integer_schema)
)
s_aspect_ratio = openapi.s_query(
    name="aspect_ratio",
    schema=z.list(z.transform(numfilter.parse_constraints)),
    py_default=None,
)


class WallpaperUploaderListAPI(APIView):
    __get_response_schema = _paging_response_schema(uploader_struct)

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["rest"])
    @openapi.apply_signature
    def get(
        self,
        request,
        wallpaper_id=s_path_wallpaper_id,
        page=s_query_page,
        page_size=s_query_page_size,
    ):
        queryset = models.Uploader.objects.filter(
            uploaderwallpaper__wallpaper_id=wallpaper_id
        )
        return JsonResponse(
            self.__get_response_schema.parse(
                _paging_response(queryset, page, page_size)
            )
        )


class TopicWallpaperListAPI(APIView):
    __get_response_schema = _paging_response_schema(wallpaper_struct)

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["rest"])
    @openapi.apply_signature
    def get(
        self,
        request,
        topic_id=s_path_topic_id,
        page=s_query_page,
        page_size=s_query_page_size,
        sizes=s_query_image_sizes,
        aspect_ratio=s_aspect_ratio,
        user=s_authenticate(required=False),
    ):
        queryset = models.WallpaperImage.objects.filter(
            topicwallpaper__topic_id=topic_id
        ).order_by("-pk")

        if aspect_ratio:
            from django.db.models import Q

            # 构建 OR 查询：每组之间是 OR 关系，组内是 AND 关系
            q_objects = []

            for constraint_group in aspect_ratio:
                # 每个组内的约束是 AND 关系
                group_q = Q()

                for constraint in constraint_group:
                    if constraint.operator == numfilter.Operator.EQ:
                        group_q &= Q(aspect_ratio=constraint.value)
                    elif constraint.operator == numfilter.Operator.GTE:
                        group_q &= Q(aspect_ratio__gte=constraint.value)
                    elif constraint.operator == numfilter.Operator.LTE:
                        group_q &= Q(aspect_ratio__lte=constraint.value)
                    elif constraint.operator == numfilter.Operator.GT:
                        group_q &= Q(aspect_ratio__gt=constraint.value)
                    elif constraint.operator == numfilter.Operator.LT:
                        group_q &= Q(aspect_ratio__lt=constraint.value)

                if group_q.children:  # 只有当组内有约束时才添加
                    q_objects.append(group_q)

            # 将所有组用 OR 连接
            if q_objects:
                final_q = q_objects[0]
                for q in q_objects[1:]:
                    final_q |= q
                queryset = queryset.filter(final_q)

        results = []
        for item in _paginate(queryset, page, page_size):
            if sizes:
                item.set_images(sizes)
            results.append(item)
        return JsonResponse(
            self.__get_response_schema.parse(
                _paging_response_format(results, page, page_size, queryset.count())
            )
        )


class ClientClienttopicListAPI(APIView):
    __get_response_schema = _paging_response_schema(clienttopic_struct)

    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.declare(tags=["rest"])
    @openapi.apply_signature
    def get(
        self,
        request,
        client_id=s_path_client_id,
        page=s_query_page,
        page_size=s_query_page_size,
        include=openapi.s_query(
            name="include",
            schema=z.list(
                z.str().ensure(
                    lambda x: x in ["topic"], meta={"oas": {"enum": ["topic"]}}
                )
            ),
            py_default=None,
        ),
    ):
        queryset = models.ClientTopic.objects.filter(client_id=client_id).order_by(
            "-pk"
        )
        if include and "topic" in include:
            queryset = queryset.select_related("topic")
        return JsonResponse(
            self.__get_response_schema.parse(
                _paging_response(queryset, page, page_size)
            )
        )


class WallpaperSimilarAPI(APIView):
    __get_response_schema = z.struct(
        {
            "results": z.to.list(
                z.struct(
                    {
                        "wallpaper": wallpaper_struct,
                        "score": z.float(),
                    }
                ),
            ),
            "limit": z.int().gte(1),
            "offset": z.int().gte(0),
            "has_next": z.bool(meta={"oas": {"description": "是否还有下一页"}}),
        }
    )

    @openapi.declare(tags=["rest"])
    @openapi.response(
        200,
        content={"application/json": openapi.MediaType(schema=__get_response_schema)},
    )
    @openapi.apply_signature
    def get(
        self,
        request,
        wallpaper_id=s_path_wallpaper_id,
        sizes=s_query_image_sizes,
        limit=openapi.s_query(schema=z.to.int().gte(1).lte(100), py_default=10),
        offset=openapi.s_query(schema=z.to.int().gte(0).lte(1000), py_default=0),
        score_threshold=openapi.s_query(schema=z.to.float(), py_default=None),
        user=s_authenticate(required=False),
    ):
        from wallpapers.services import qdrant

        try:
            wallpaper = models.WallpaperImage.objects.get(pk=wallpaper_id)
        except models.WallpaperImage.DoesNotExist:
            raise Http404
        results, has_next = qdrant.get_similar_wallpapers(
            wallpaper, limit=limit, offset=offset, score_threshold=score_threshold
        )

        if sizes:
            for item in results:
                item[0].set_images(sizes)

        return JsonResponse(
            self.__get_response_schema.parse(
                {
                    "results": [
                        {"wallpaper": wp, "score": score} for wp, score in results
                    ],
                    "limit": limit,
                    "offset": offset,
                    "has_next": has_next,
                }
            )
        )
