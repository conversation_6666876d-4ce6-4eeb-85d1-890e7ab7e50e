import zangar as z
from django.db.models import Model

from wallpapers import models
from wallpapers.auth import user_ctx


def _foreign_field(schema: z.<PERSON>, name: str):
    def getter(o: Model):
        if name not in o._state.fields_cache:  # type: ignore
            raise AttributeError(name)
        return getattr(o, name)

    return z.field(schema, getter=getter)


_image_url_faker = {
    "helpers.arrayElement": [
        [
            "https://dummyimage.com/300x400",
            "https://dummyimage.com/800x200",
        ]
    ]
}

sql_integer_schema = z.int().gte(-(2**63)).lte(2**63 - 1)


def _url_getter(o: models.WallpaperImage):
    if not user_ctx.get(None):
        raise Exception("Unauthorized")
    return o.url


wallpaper_struct = z.struct(
    {
        "id": z.int(),
        "url": z.field(
            z.str(
                meta={
                    "oas": {"description": "原始图片地址，该字段需要管理员身份才能获取"}
                },
            ),
            getter=_url_getter,
        ).optional(),
        "images": z.field(
            z.ensure(
                lambda x: isinstance(x, dict),
                meta={
                    "oas": {
                        "type": "object",
                        "properties": {
                            "default": {
                                "type": "string",
                                "x-faker": _image_url_faker,
                            }
                        },
                        "additionalProperties": {
                            "type": "string",
                            "x-faker": _image_url_faker,
                        },
                        "description": "图片列表，当未提供任何 size 时，将默认填充一个 default 字段",
                    }
                },
            ),
        ),
        "format": z.str(),
        "width": z.int(),
        "height": z.int(),
        "filesize": z.int(meta={"oas": {"description": "单位为字节"}}),
        "content_md5": z.str(),
    }
)
uploader_struct = z.struct(
    {
        "id": z.int(),
        "name": z.str(),
    }
)
topic_struct = z.struct(
    {
        "id": z.int(),
    }
)
clienttopic_struct = z.struct(
    {
        "id": z.int(),
        "title": z.str(),
        "topic_id": z.int(),
        "topic": _foreign_field(topic_struct, "topic").optional(),
        "client_id": z.to.str(),
    }
)
