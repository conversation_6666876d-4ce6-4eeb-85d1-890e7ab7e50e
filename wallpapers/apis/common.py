import re
from inspect import cleandoc

import zangar as z

import openapi


def _split_size(size: str) -> tuple[int, int]:
    width, height = map(int, size.split("x", 1))
    return width, height


s_query_image_sizes = openapi.s_query(
    name="size",
    schema=z.list(
        z.str().ensure(lambda x: bool(re.match(r"^\d+x\d+$", x))).transform(_split_size)
    ),
    py_default=None,
    description=cleandoc(
        """
        设置图片大小在该尺寸以内(不会改变图片原始比例)，格式为 `宽x高`，如 "1920x1080"。

        若其中一个值为 0，则表示不进行限制。如 "0x1080" 表示宽度不限，高度为 1080。

        当提供了 `size` 字段后，壁纸字段 `images` 会将 `size` 作为键并设置图片地址。
    """
    ),
)
